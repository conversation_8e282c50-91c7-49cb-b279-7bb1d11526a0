from telethon import TelegramClient, events
import asyncio
import re
import logging
import os
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import PatternFill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Litaipump Scanner specific credentials
API_ID = os.getenv("LITAIPUMP_API_ID")
API_HASH = os.getenv("LITAIPUMP_API_HASH")
PHONE = os.getenv("LITAIPUMP_PHONE")
DESTINATION_BOT = os.getenv("SCANNER_BOT", "@soul_scanner_bot")

# Litaipump-specific configuration
SOURCE_USERNAME = "@LIT_AI_Pump_Fun_New_Tokens"
SESSION_FILE = "Litaipump/Litaipump_Scanner.session"  # Session inside folder
EXCEL_FILE = "Litaipump_Scanner_data.xlsx"           # Excel in main directory
WHITELIST = ["pump info"]
BLACKLIST = ["SellAmount"]

# <PERSON>ript settings
RATE_LIMIT_DELAY = 5
WAIT_TIME = 5

# Global variables
client = None
processed_tokens = set()
processing_lock = asyncio.Lock()
last_processed_message_id = None

HEADERS = [
    "Timestamp", "Token Address", "Token Name", "Warnings", "Age", "MC", "T-MC",
    "Liq", "Liq SOL", "First 20 Percentage", "First 20", "Whale Fish Pattern",
    "Made", "Volume", "Price", "Scans", "Hodls", "Top Holders", "Snipers",
    "Snipers Percentage", "Dex Paid", "High", "Dev", "LP Burnt", "Bundled", "Airdrop", "Burnt",
    "Sold", "Bond"
]

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', handlers=[logging.StreamHandler()])
logger = logging.getLogger(__name__)

def initialize_excel():
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created new Excel file: {EXCEL_FILE}")
    else:
        logger.info(f"📊 Using existing Excel file: {EXCEL_FILE}")

def extract_token_from_text(message_text):
    # Litaipump uses CA: pattern
    token_pattern = r"CA:\s*([A-Za-z0-9]{43,44})\s*📋"
    token_matches = re.findall(token_pattern, message_text)
    if token_matches:
        return token_matches[0]
    return None

def extract_data_from_scanner_response(message_text, token_address):
    data = {
        "Token Address": token_address, "Token Name": "N/A", "Warnings": "N/A", "Age": "N/A",
        "MC": "N/A", "T-MC": "N/A", "Liq": "N/A", "Liq SOL": "N/A", "Volume": "N/A", "Price": "N/A", "Dex Paid": "N/A",
        "Scans": "N/A", "Hodls": "N/A", "High": "N/A", "Snipers": "N/A", "Snipers Percentage": "N/A", "Dev": "N/A",
        "Top Holders": "N/A", "LP Burnt": "N/A", "Bundled": "N/A", "Airdrop": "N/A", "Burnt": "N/A", "Sold": "N/A",
        "Made": "N/A", "Bond": "N/A", "First 20 Percentage": "N/A", "First 20": "N/A", "Whale Fish Pattern": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings extraction
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [f"{icon} {warning}" if icon else warning for icon, warning in warnings if any(keyword.lower() in warning.lower() for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* ([^\n]+)", message_text)
    if age_match:
        data["Age"] = age_match.group(1).strip()

    # MC (Market Cap)
    mc_match = re.search(r"💰 \*\*MC:\*\* ([^\s•]+)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1).strip()

    # T-MC (Target Market Cap)
    t_mc_match = re.search(r"🔝 __([^_]+)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1).strip()

    # Liquidity
    liq_match = re.search(r"💧 \*\*Liq:\*\* ([^\s•]+)", message_text)
    if liq_match:
        data["Liq"] = liq_match.group(1).strip()

    # Liquidity SOL
    liq_sol_match = re.search(r"💧.*?(\d+\.?\d* SOL)", message_text)
    if liq_sol_match:
        data["Liq SOL"] = liq_sol_match.group(1)

    # Volume
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", message_text)
    if volume_match:
        data["Volume"] = volume_match.group(1).strip()

    # Price
    price_match = re.search(r"📈 \*\*Price:\*\* ([^*\n]+)", message_text)
    if price_match:
        data["Price"] = price_match.group(1).strip()

    # Dex Paid
    if "✅" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "✅"
    elif "❌" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "❌"

    # Scans
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High
    high_match = re.search(r"┗ High: \[(\d+\.?\d*%)\]", message_text)
    if not high_match:
        high_match = re.search(r"┗ High: (\d+\.?\d*%)", message_text)
    if not high_match:
        high_match = re.search(r"High: (\d+\.?\d*%)", message_text)
    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🎯 \*\*Snipers:\*\* (\d+)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)

    # Snipers Percentage
    snipers_percentage_match = re.search(r"🎯.*?(\d+\.?\d*%)", message_text)
    if snipers_percentage_match:
        data["Snipers Percentage"] = snipers_percentage_match.group(1)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Top Holders
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP Burnt
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP Burnt"] = lp_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Made patterns
    made_match = re.search(r"Made: (\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)

    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", message_text)
    if bond_match:
        data["Bond"] = bond_match.group(1)

    # Bundled patterns
    bundled_match = re.search(r"Bundled: (\d+%)", message_text)
    if bundled_match:
        data["Bundled"] = bundled_match.group(1)

    # Airdrop patterns
    airdrop_match = re.search(r"Airdrop: (\d+%)", message_text)
    if airdrop_match:
        data["Airdrop"] = airdrop_match.group(1)

    # Burnt patterns
    burnt_match = re.search(r"Burnt: ([^🔥\n]+)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1).strip()

    # First 20 detailed patterns
    first20_detailed = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", message_text)
    if first20_detailed:
        data["First 20 Percentage"] = first20_detailed.group(1)
        count = first20_detailed.group(2)
        emoji = first20_detailed.group(3)
        percentage = first20_detailed.group(4)
        data["First 20"] = f"{count} {emoji} • {percentage}"

    # Extract whale/fish patterns
    holder_pattern_match = re.findall(r'\[([🛠🐟🍤🐳🌱])\]\(https://solscan\.io/account/[^)]+\)', message_text)
    if holder_pattern_match:
        all_patterns = ''.join(holder_pattern_match)
        data['Whale Fish Pattern'] = all_patterns

    return data

def parse_market_cap_value(mc_text):
    if not mc_text or mc_text == "N/A":
        return 0
    mc_clean = mc_text.replace("$", "").replace(",", "").upper()
    try:
        if "K" in mc_clean:
            return float(mc_clean.replace("K", ""))
        elif "M" in mc_clean:
            return float(mc_clean.replace("M", "")) * 1000
        elif "B" in mc_clean:
            return float(mc_clean.replace("B", "")) * 1000000
        else:
            return float(mc_clean) / 1000
    except (ValueError, AttributeError):
        return 0

def get_tmc_color(tmc_value_k):
    if 30 <= tmc_value_k <= 60:
        return PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    elif 61 <= tmc_value_k <= 100:
        return PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
    elif tmc_value_k >= 101:
        return PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
    else:
        return None

async def export_to_excel(data):
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active
            token_exists = False
            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data.get("Token Address"):
                    token_exists = True
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping")
                    return
            if not token_exists:
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [morocco_time, data["Token Address"], data["Token Name"], data["Warnings"], data["Age"], data["MC"], data["T-MC"], data["Liq"], data["Liq SOL"], data["First 20 Percentage"], data["First 20"], data["Whale Fish Pattern"], data["Made"], data["Volume"], data["Price"], data["Scans"], data["Hodls"], data["Top Holders"], data["Snipers"], data["Snipers Percentage"], data["Dex Paid"], data["High"], data["Dev"], data["LP Burnt"], data["Bundled"], data["Airdrop"], data["Burnt"], data["Sold"], data["Bond"]]
                ws.append(row)
                current_row = ws.max_row
                tmc_column_index = 7
                tmc_cell = ws.cell(row=current_row, column=tmc_column_index)
                tmc_value_k = parse_market_cap_value(data["T-MC"])
                tmc_color = get_tmc_color(tmc_value_k)
                if tmc_color:
                    tmc_cell.fill = tmc_color
                    logger.info(f"🎨 Applied color to T-MC: {data['T-MC']} (${tmc_value_k}K)")
                logger.info(f"✅ Added new row for token {data['Token Address']}")
            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")

async def forward_token(token_address):
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        logger.error(f"❌ Failed to forward token: {e}")

async def process_one_message():
    global last_processed_message_id
    async with processing_lock:
        messages = await client.get_messages(SOURCE_USERNAME, limit=1)
        if not messages:
            logger.info("⚠️ No new messages found.")
            return
        message = messages[0]
        if last_processed_message_id and message.id <= last_processed_message_id:
            logger.info("⚠️ No new messages to process.")
            return
        last_processed_message_id = message.id
        message_text = message.message
        if message_text is None:
            logger.info("⚠️ Message contains no text. Skipping...")
            return
        logger.info(f"📩 New message from {SOURCE_USERNAME}: {message_text}")
        token_address = extract_token_from_text(message_text)
        if not token_address:
            logger.info("⚠️ No token found in the message.")
            return
        if token_address in processed_tokens:
            logger.info(f"⚠️ Token already processed: {token_address}")
            return
        message_text_lower = message_text.lower()
        if WHITELIST and not any(word.lower() in message_text_lower for word in WHITELIST):
            logger.info("⚠️ Message does not contain whitelist keywords.")
            return
        if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
            logger.info("⚠️ Message contains blacklist keywords.")
            return
        processed_tokens.add(token_address)
        logger.info(f"🎯 Found token: {token_address}")
        await forward_token(token_address)
        logger.info(f"⏳ Waiting for {WAIT_TIME} seconds...")
        await asyncio.sleep(WAIT_TIME)
        dest_messages = await client.get_messages(DESTINATION_BOT, limit=1)
        if not dest_messages:
            logger.info("⚠️ No response from destination bot.")
            return
        last_dest_message = dest_messages[0]
        logger.info(f"📩 Destination bot response: {last_dest_message.text}")
        combined_data = extract_data_from_scanner_response(last_dest_message.text, token_address)
        await export_to_excel(combined_data)
        logger.info("🛑 Finished processing message.")

async def main():
    global client
    logger.info("🚀 Starting Litaipump Scanner")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Destination: {DESTINATION_BOT}")
    logger.info(f"📊 Excel File: {EXCEL_FILE}")
    logger.info("=" * 50)
    initialize_excel()
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)
    try:
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")
        while True:
            await process_one_message()
            await asyncio.sleep(RATE_LIMIT_DELAY)
    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())
