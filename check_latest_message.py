#!/usr/bin/env python3
"""
Check the latest message from @solearlytrending to see the exact format
"""

from telethon import TelegramClient
import asyncio
import re
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Early Scanner specific credentials
API_ID = os.getenv("EARLY_API_ID")
API_HASH = os.getenv("EARLY_API_HASH")
PHONE = os.getenv("EARLY_PHONE")

# Configuration
SOURCE_USERNAME = "@solearlytrending"
SESSION_FILE = "Early/Early_Scanner.session"

# Token patterns
TOKEN_PATTERN_TYPE1 = r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})"
TOKEN_PATTERN_TYPE2 = r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})"
DIRECT_TOKEN_PATTERN = r"([A-Za-z0-9]{39,44}pump)"

# Message filtering patterns
TYPE1_PATTERN = ["New Trending"]

# Logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_all_tokens_from_message(message):
    """Extract ALL possible token addresses from a message"""
    tokens_found = set()
    message_text = message.message or ""
    
    logger.info(f"🔍 Analyzing message text...")
    logger.info(f"📝 Message: {message_text}")
    
    # Method 1: Direct token pattern in text
    direct_matches = re.findall(DIRECT_TOKEN_PATTERN, message_text)
    for token in direct_matches:
        tokens_found.add(token)
        logger.info(f"🎯 Found token in TEXT (direct): {token}")
    
    # Method 2: Type 1 pattern in text
    type1_matches = re.findall(TOKEN_PATTERN_TYPE1, message_text)
    for token in type1_matches:
        tokens_found.add(token)
        logger.info(f"🎯 Found token in TEXT (Type 1): {token}")
    
    # Method 3: Type 2 pattern in text
    type2_matches = re.findall(TOKEN_PATTERN_TYPE2, message_text)
    for token in type2_matches:
        tokens_found.add(token)
        logger.info(f"🎯 Found token in TEXT (Type 2): {token}")
    
    # Method 4: Check entities
    if message.entities:
        logger.info(f"🔍 Checking {len(message.entities)} entities...")
        for i, entity in enumerate(message.entities):
            if hasattr(entity, 'url') and entity.url:
                url = entity.url
                logger.info(f"🔗 Entity {i} URL: {url}")
                
                # Check all patterns in entity URLs
                type1_matches = re.findall(TOKEN_PATTERN_TYPE1, url)
                for token in type1_matches:
                    tokens_found.add(token)
                    logger.info(f"🎯 Found token in ENTITY {i} (Type 1): {token}")
                
                type2_matches = re.findall(TOKEN_PATTERN_TYPE2, url)
                for token in type2_matches:
                    tokens_found.add(token)
                    logger.info(f"🎯 Found token in ENTITY {i} (Type 2): {token}")
                
                # Also check for direct tokens in URLs
                direct_matches = re.findall(DIRECT_TOKEN_PATTERN, url)
                for token in direct_matches:
                    tokens_found.add(token)
                    logger.info(f"🎯 Found token in ENTITY {i} (direct): {token}")
    
    # Method 5: Check buttons
    if message.buttons:
        logger.info(f"🔍 Checking {len(message.buttons)} button rows...")
        for row_i, button_row in enumerate(message.buttons):
            for btn_i, button in enumerate(button_row):
                if hasattr(button, 'url') and button.url:
                    url = button.url
                    logger.info(f"🔗 Button {row_i}-{btn_i} URL: {url}")
                    
                    # Check all patterns in button URLs
                    type1_matches = re.findall(TOKEN_PATTERN_TYPE1, url)
                    for token in type1_matches:
                        tokens_found.add(token)
                        logger.info(f"🎯 Found token in BUTTON {row_i}-{btn_i} (Type 1): {token}")
                    
                    type2_matches = re.findall(TOKEN_PATTERN_TYPE2, url)
                    for token in type2_matches:
                        tokens_found.add(token)
                        logger.info(f"🎯 Found token in BUTTON {row_i}-{btn_i} (Type 2): {token}")
                    
                    direct_matches = re.findall(DIRECT_TOKEN_PATTERN, url)
                    for token in direct_matches:
                        tokens_found.add(token)
                        logger.info(f"🎯 Found token in BUTTON {row_i}-{btn_i} (direct): {token}")
    
    return list(tokens_found)

async def main():
    """Main function to check the latest message"""
    logger.info("🚀 Checking latest message from @solearlytrending")
    
    # Initialize Telegram client
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)
    
    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")
        
        # Get the latest message
        logger.info("📥 Fetching latest message...")
        messages = await client.get_messages(SOURCE_USERNAME, limit=1)
        
        if not messages:
            logger.info("❌ No messages found")
            return
        
        message = messages[0]
        message_text = message.message or ""
        
        logger.info("=" * 80)
        logger.info(f"📩 LATEST MESSAGE")
        logger.info(f"🆔 Message ID: {message.id}")
        logger.info(f"📅 Date: {message.date}")
        logger.info("=" * 80)
        
        # Check if it contains "New Trending"
        has_new_trending = any(pattern.lower() in message_text.lower() for pattern in TYPE1_PATTERN)
        logger.info(f"🔍 Contains 'New Trending': {has_new_trending}")
        
        if has_new_trending:
            logger.info("✅ This message should be processed by Early_Scanner!")
            
            # Extract all possible tokens
            tokens = extract_all_tokens_from_message(message)
            
            if tokens:
                logger.info(f"🎯 TOTAL TOKENS FOUND: {len(tokens)}")
                for i, token in enumerate(tokens, 1):
                    logger.info(f"  {i}. {token} (ends with 'pump': {token.endswith('pump')})")
                
                # Find the main token (usually the first one or the one in soul_sniper_bot URL)
                main_token = None
                for token in tokens:
                    if token.endswith('pump'):
                        main_token = token
                        break
                
                if main_token:
                    logger.info(f"🎯 MAIN TOKEN: {main_token}")
                    logger.info("✅ This token should be forwarded to @soul_scanner_bot")
                else:
                    logger.info("⚠️ No token ending with 'pump' found")
            else:
                logger.info("❌ No tokens found in this message")
        else:
            logger.info("⚠️ This message does not contain 'New Trending'")
        
        logger.info("=" * 80)
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
    finally:
        if client:
            await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())
