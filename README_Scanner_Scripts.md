# SolSheet Scanner Scripts

I have successfully created 8 scanner scripts based on your existing `Soul_Scanner.py` structure. Each script monitors a different source and creates its own Excel sheet with the same data structure as your original scanner.

## Scripts Created

### 1. ATH_Scanner.py
- **Source**: `@gmgnsignals`
- **Whitelist**: ["ATH Price"]
- **Excel File**: `ATH/ATH_Scanner_data.xlsx`
- **Session File**: `ATH/ATH_Scanner.session`
- **Token Pattern**: Tokens ending with "pump" (39-40 chars + "pump")
- **Special Feature**: Extracts source age from "Open: X ago" pattern

### 2. Early_Scanner.py ⭐ **ENHANCED WITH PROFIT TRACKING**
- **Source**: `@solearlytrending`
- **Excel File**: `Early/Early_Scanner_data.xlsx`
- **Session File**: `Early/Early_Scanner.session`
- **Message Filtering**:
  - **Type 1 Pattern**: ["New Trending"] - Only processes messages containing "New Trending"
  - **Type 2 Pattern**: ["is up"] - Only processes messages containing "is up"
- **Token Patterns**:
  - **Type 1**: `https://t.me/soul_sniper_bot?start=15_[TOKEN]` (New tokens)
  - **Type 2**: `https://www.geckoterminal.com/solana/pools/[TOKEN]` (Profit updates)
- **Special Features**:
  - **Profit Column**: Tracks profit from "is up" messages
  - **Color Coding**:
    - Grey: 50%-99%
    - Yellow: 2X-2.9X
    - Purple: 3X-3.9X
    - Red: 5X-5.9X
    - Orange: 7X-7.9X
    - Green: 10X-19.9X
    - Blue: 20X+
  - **Dual Message Processing**:
    - **Type 1**: "New Trending" messages → Extract Type 1 token → Full scanner data
    - **Type 2**: "is up" messages → Extract Type 2 token → Update profit column only

### 3. FDV1_Scanner.py
- **Source**: `@gmgnsignals`
- **Whitelist**: ["FDV in 5 min"]
- **Excel File**: `FDV1/FDV1_Scanner_data.xlsx`
- **Session File**: `FDV1/FDV1_Scanner.session`
- **Token Pattern**: Tokens ending with "pump" (39-40 chars + "pump")
- **Special Feature**: Extracts source age from "Open: X ago" pattern

### 4. FDV2_Scanner.py
- **Source**: `@gmgnsignalsol`
- **Whitelist**: ["FDV in 5 min"]
- **Excel File**: `FDV2/FDV2_Scanner_data.xlsx`
- **Session File**: `FDV2/FDV2_Scanner.session`
- **Token Pattern**: Tokens ending with "pump" (39-40 chars + "pump")
- **Special Feature**: Extracts source age from "Open: X ago" pattern

### 5. Litaipump_Scanner.py
- **Source**: `@LIT_AI_Pump_Fun_New_Tokens`
- **Whitelist**: ["pump info"]
- **Excel File**: `Litaipump/Litaipump_Scanner_data.xlsx`
- **Session File**: `Litaipump/Litaipump_Scanner.session`
- **Token Pattern**: `CA: [TOKEN] 📋` format
- **Rate Limit**: 5 seconds (higher due to source characteristics)

### 6. Solbix_Scanner.py
- **Source**: `@csolbix`
- **Whitelist**: [] (processes all messages)
- **Excel File**: `Solbix/Solbix_Scanner_data.xlsx`
- **Session File**: `Solbix/Solbix_Scanner.session`
- **Token Pattern**: General 43-44 character Solana tokens

### 7. Newpool_Scanner.py
- **Source**: Channel ID `-1002122751413`
- **Whitelist**: ["NewPool"]
- **Excel File**: `newpool/Newpool_Scanner_data.xlsx`
- **Session File**: `newpool/Newpool_Scanner.session`
- **Token Pattern**: Extracts from `https://gmgn.ai/sol/token/[TOKEN]` URLs
- **Special Feature**: Uses event-driven message handling for channel monitoring

### 8. Pump_King_Scanner.py
- **Source**: `@gmgnsignals`
- **Whitelist**: ["KOTH"]
- **Excel File**: `pump_king/Pump_King_Scanner_data.xlsx`
- **Session File**: `pump_king/Pump_King_Scanner.session`
- **Token Pattern**: Tokens ending with "pump" (39-40 chars + "pump")
- **Special Feature**: Extracts source age from "Open: X ago" pattern

## Common Features

All scripts share these features from your original `Soul_Scanner.py`:

### Data Structure
- Same Excel headers and column order as your original
- T-MC column coloring (Yellow: $30K-60K, Light Green: $61K-100K, Light Blue: $101K+)
- Morocco timezone timestamps
- Duplicate token detection and prevention

### Data Extraction
- Token Name, Warnings, Age, MC, T-MC, Liquidity, Volume, Price
- Scans, Hodls, High percentage, Snipers data
- Dev holdings, Top Holders, LP Burnt status
- First 20 holder patterns and whale/fish emoji patterns
- Made, Bond, Bundled, Airdrop, Burnt, Sold data

### Technical Features
- Async processing with locks to prevent race conditions
- Rate limiting between messages
- Error handling and logging
- Environment variable support for credentials
- Automatic directory creation for Excel files

## Configuration

All scripts use the same environment variables:
```
API_ID=your_telegram_api_id
API_HASH=your_telegram_api_hash
PHONE=your_phone_number
SOUL_SCANNER_BOT=@soul_scanner_bot
```

## Usage

Each script can be run independently:
```bash
python ATH_Scanner.py
python Early_Scanner.py
python FDV1_Scanner.py
# ... etc
```

## File Structure

The scripts will create the following directory structure:
```
SolSheet/
├── ATH/
│   ├── ATH_Scanner_data.xlsx
│   └── ATH_Scanner.session
├── Early/
│   ├── Early_Scanner_data.xlsx
│   └── Early_Scanner.session
├── FDV1/
│   ├── FDV1_Scanner_data.xlsx
│   └── FDV1_Scanner.session
├── FDV2/
│   ├── FDV2_Scanner_data.xlsx
│   └── FDV2_Scanner.session
├── Litaipump/
│   ├── Litaipump_Scanner_data.xlsx
│   └── Litaipump_Scanner.session
├── Solbix/
│   ├── Solbix_Scanner_data.xlsx
│   └── Solbix_Scanner.session
├── newpool/
│   ├── Newpool_Scanner_data.xlsx
│   └── Newpool_Scanner.session
├── pump_king/
│   ├── Pump_King_Scanner_data.xlsx
│   └── Pump_King_Scanner.session
└── soul_scanner_data.xlsx (original)
```

## Notes

1. **Session Files**: Each script uses its own session file to maintain separate Telegram connections
2. **Rate Limiting**: Different sources have different rate limits based on their message frequency
3. **Token Patterns**: Each source has specific token extraction patterns based on their message format
4. **Whitelist/Blacklist**: Customized for each source's specific keywords
5. **Error Handling**: All scripts include comprehensive error handling and logging

All scripts are ready to run and will create their respective Excel files with the same data structure as your original `Soul_Scanner.py`!
