from telethon import Telegram<PERSON>lient, events
import asyncio
import re
import logging
import os
from datetime import datetime
import pytz
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Pat<PERSON><PERSON>ill
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Early Scanner specific credentials
API_ID = os.getenv("EARLY_API_ID")
API_HASH = os.getenv("EARLY_API_HASH")
PHONE = os.getenv("EARLY_PHONE")
DESTINATION_BOT = os.getenv("SCANNER_BOT", "@soul_scanner_bot")

# Early-specific configuration
SOURCE_USERNAME = "@solearlytrending"
SESSION_FILE = "Early/Early_Scanner.session"  # Session inside folder
EXCEL_FILE = "Early_Scanner_data.xlsx"        # Excel in main directory
WHITELIST = []  # Process all messages from early trending
BLACKLIST = ["SellAmount"]

# Token patterns for early trending
TOKEN_PATTERN_TYPE1 = r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})"
TOKEN_PATTERN_TYPE2 = r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})"

# Message filtering patterns
TYPE1_PATTERN = ["New Trending"]  # Process Type 1 messages containing these words
TYPE2_PATTERN = ["is up"]  # Process Type 2 messages containing these words

# Script settings
RATE_LIMIT_DELAY = 3
WAIT_TIME = 5

# Global variables
client = None
processed_tokens = set()
processing_lock = asyncio.Lock()
last_processed_message_id = None

HEADERS = [
    # Your specified order first
    "Timestamp", "Token Address", "Token Name", "Profit", "Warnings", "Age", "MC", "T-MC",
    "Liq", "Liq SOL", "First 20 Percentage", "First 20", "Whale Fish Pattern",
    "Made", "Volume", "Price", "Scans", "Hodls", "Top Holders", "Snipers",
    "Snipers Percentage",
    # Remaining columns in any order
    "Dex Paid", "High", "Dev", "LP Burnt", "Bundled", "Airdrop", "Burnt",
    "Sold", "Bond"
]

# Profit color fills
GREEN_FILL = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")  # 10X-19.9X
BLUE_FILL = PatternFill(start_color="0000FF", end_color="0000FF", fill_type="solid")   # >20X
ORANGE_FILL = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid") # 7X-7.9X
RED_FILL = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")    # 5X-5.9X
PURPLE_FILL = PatternFill(start_color="800080", end_color="800080", fill_type="solid") # 3X-3.9X
YELLOW_FILL = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid") # 2X-2.9X
GREY_FILL = PatternFill(start_color="808080", end_color="808080", fill_type="solid")   # 50%-99%

# Morocco timezone
MOROCCO_TZ = pytz.timezone('Africa/Casablanca')

# Logging configuration (console only)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def initialize_excel():
    """Initialize Excel file with headers if it doesn't exist"""
    if not os.path.exists(EXCEL_FILE):
        wb = Workbook()
        ws = wb.active
        ws.append(HEADERS)
        wb.save(EXCEL_FILE)
        logger.info(f"✅ Created new Excel file: {EXCEL_FILE}")
    else:
        logger.info(f"📊 Using existing Excel file: {EXCEL_FILE}")

def extract_profit_from_message(message_text):
    """Extract profit value from Type 2 message with enhanced pattern matching."""
    if not message_text:
        return None

    # 1. Match standard format: "is up 53%" or "is up 5.3X" with flexible spacing/formatting
    profit_match = re.search(r"is\s+up\s*[^\d]*(\d+\.?\d*)([%Xx])", message_text, re.IGNORECASE)
    if profit_match:
        value = profit_match.group(1)
        unit = profit_match.group(2).upper()  # Normalize unit to uppercase
        return f"{value}{unit}"

    # 2. Try arrow notation format: "$50.6K —> $77.1K 💵" with flexible arrow styles
    mc_match = re.search(r"\$(\d+\.?\d*)([KMB])?\s*(?:—>|->|=>|⇒)\s*\$(\d+\.?\d*)([KMB])?", message_text)
    if mc_match:
        # Extract values and convert to float
        start_value = float(mc_match.group(1))
        start_unit = mc_match.group(2) or ''
        end_value = float(mc_match.group(3))
        end_unit = mc_match.group(4) or ''

        # Convert to same units
        multipliers = {'': 1, 'K': 1000, 'M': 1000000, 'B': 1000000000}
        start_amount = start_value * multipliers[start_unit]
        end_amount = end_value * multipliers[end_unit]

        # Calculate percentage or X increase
        if end_amount > start_amount:
            increase_ratio = end_amount / start_amount
            if increase_ratio >= 2:
                # X format for 2X or more
                return f"{increase_ratio:.1f}X"
            else:
                # Percentage format for less than 2X
                percentage = (increase_ratio - 1) * 100
                return f"{percentage:.0f}%"

    # 3. Look for a specific pattern with the words "up" and percentage/X value near each other
    up_val_match = re.search(r"up\s+(?:by\s+)?(\d+\.?\d*)([%Xx])", message_text, re.IGNORECASE)
    if up_val_match:
        value = up_val_match.group(1)
        unit = up_val_match.group(2).upper()
        return f"{value}{unit}"

    # 4. Look for standalone values that might represent profits
    # This is the most aggressive matching and only use if nothing else works
    profit_indicators = ["profit", "gain", "up", "increased", "jumped", "gained"]
    if any(indicator in message_text.lower() for indicator in profit_indicators):
        # Look for percentage or X values
        standalone_match = re.search(r"(\d+\.?\d*)([%Xx])", message_text)
        if standalone_match:
            value = standalone_match.group(1)
            unit = standalone_match.group(2).upper()
            return f"{value}{unit}"

    return None

def extract_data_from_scanner_response(message_text, token_address):
    """Extract data from scanner bot response"""
    data = {
        "Token Address": token_address,
        "Token Name": "N/A",
        "Profit": "Lost",
        "Warnings": "N/A",
        "Age": "N/A",
        "MC": "N/A",
        "T-MC": "N/A",
        "Liq": "N/A",
        "Liq SOL": "N/A",
        "Volume": "N/A",
        "Price": "N/A",
        "Dex Paid": "N/A",
        "Scans": "N/A",
        "Hodls": "N/A",
        "High": "N/A",
        "Snipers": "N/A",
        "Snipers Percentage": "N/A",
        "Dev": "N/A",
        "Top Holders": "N/A",
        "LP Burnt": "N/A",
        "Bundled": "N/A",
        "Airdrop": "N/A",
        "Burnt": "N/A",
        "Sold": "N/A",
        "Made": "N/A",
        "Bond": "N/A",
        "First 20 Percentage": "N/A",
        "First 20": "N/A",
        "Whale Fish Pattern": "N/A"
    }

    # Token Name
    token_name_match = re.search(r"\*\*\$([A-Za-z0-9]+)\*\*", message_text)
    if token_name_match:
        data["Token Name"] = token_name_match.group(1)

    # Warnings extraction
    warnings = re.findall(r"(🚨|⚠️)?\s*__([^__]+)__", message_text)
    if warnings:
        filtered_warnings = [
            f"{icon} {warning}" if icon else warning
            for icon, warning in warnings
            if any(keyword.lower() in warning.lower()
                 for keyword in ["High", "Migrating", "Low", "Dev", "Sniped", "Abnormal"])
        ]
        if filtered_warnings:
            data["Warnings"] = " | ".join(filtered_warnings)

    # Age
    age_match = re.search(r"🕒 \*\*Age:\*\* ([^\n]+)", message_text)
    if age_match:
        data["Age"] = age_match.group(1).strip()

    # MC (Market Cap)
    mc_match = re.search(r"💰 \*\*MC:\*\* ([^\s•]+)", message_text)
    if mc_match:
        data["MC"] = mc_match.group(1).strip()

    # T-MC (Target Market Cap)
    t_mc_match = re.search(r"🔝 __([^_]+)__", message_text)
    if t_mc_match:
        data["T-MC"] = t_mc_match.group(1).strip()

    # Liquidity
    liq_match = re.search(r"💧 \*\*Liq:\*\* ([^\s•]+)", message_text)
    if liq_match:
        data["Liq"] = liq_match.group(1).strip()

    # Liquidity SOL
    liq_sol_match = re.search(r"💧.*?(\d+\.?\d* SOL)", message_text)
    if liq_sol_match:
        data["Liq SOL"] = liq_sol_match.group(1)

    # Volume - Updated pattern for: 📈 **Vol:** __1h__: $527.9K | __1d__: $1.4M
    volume_match = re.search(r"📈 \*\*Vol:\*\* ([^📈\n]+)", message_text)
    if volume_match:
        data["Volume"] = volume_match.group(1).strip()

    # Price
    price_match = re.search(r"📈 \*\*Price:\*\* ([^*\n]+)", message_text)
    if price_match:
        data["Price"] = price_match.group(1).strip()

    # Dex Paid
    if "✅" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "✅"
    elif "❌" in message_text and "Dex" in message_text:
        data["Dex Paid"] = "❌"

    # Scans - Updated pattern for: ⚡ **Scans:** 627 | 🔗 [X](https://x.com/...)
    scans_match = re.search(r"⚡ \*\*Scans:\*\* (\d+)", message_text)
    if scans_match:
        data["Scans"] = scans_match.group(1)

    # Hodls - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    hodls_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): ([\d,]+)", message_text)
    if hodls_match:
        data["Hodls"] = hodls_match.group(1)

    # High - Multiple patterns for: ┗ High: [5.2%](link) or ┗ High: 5.2% or High: 5.2%
    high_match = re.search(r"┗ High: \[(\d+\.?\d*%)\]", message_text)  # [5.2%](link) format
    if not high_match:
        high_match = re.search(r"┗ High: (\d+\.?\d*%)", message_text)  # 5.2% format
    if not high_match:
        # Alternative pattern for other High formats
        high_match = re.search(r"High: (\d+\.?\d*%)", message_text)

    if high_match:
        data["High"] = high_match.group(1)

    # Snipers
    snipers_match = re.search(r"🎯 \*\*Snipers:\*\* (\d+)", message_text)
    if snipers_match:
        data["Snipers"] = snipers_match.group(1)

    # Snipers Percentage
    snipers_percentage_match = re.search(r"🎯.*?(\d+\.?\d*%)", message_text)
    if snipers_percentage_match:
        data["Snipers Percentage"] = snipers_percentage_match.group(1)

    # Dev
    dev_match = re.search(r"🛠️ Dev.*?(\d+ SOL)", message_text)
    if dev_match:
        data["Dev"] = dev_match.group(1)

    # Top Holders - Updated pattern for: **👥 **[**Hodls**](...): 1,438 • Top: 15.5%
    top_holders_match = re.search(r"\*\*👥 \*\*\[\*\*Hodls\*\*\]\([^)]+\): [\d,]+ • Top: (\d+\.?\d*%)", message_text)
    if top_holders_match:
        data["Top Holders"] = top_holders_match.group(1)

    # LP Burnt
    lp_match = re.search(r"\*\*:\*\* (\d+%) Burnt", message_text)
    if lp_match:
        data["LP Burnt"] = lp_match.group(1)

    # Sold
    sold_match = re.search(r"\| Sold:\s*(\d+\.?\d*%)", message_text)
    if sold_match:
        data["Sold"] = sold_match.group(1)

    # Made patterns
    made_match = re.search(r"Made: (\d+)", message_text)
    if made_match:
        data["Made"] = made_match.group(1)

    # Bond patterns
    bond_match = re.search(r"Bond: (\d+)", message_text)
    if bond_match:
        data["Bond"] = bond_match.group(1)

    # Bundled patterns
    bundled_match = re.search(r"Bundled: (\d+%)", message_text)
    if bundled_match:
        data["Bundled"] = bundled_match.group(1)

    # Airdrop patterns
    airdrop_match = re.search(r"Airdrop: (\d+%)", message_text)
    if airdrop_match:
        data["Airdrop"] = airdrop_match.group(1)

    # Burnt patterns (different from LP Burnt)
    burnt_match = re.search(r"Burnt: ([^🔥\n]+)", message_text)
    if burnt_match:
        data["Burnt"] = burnt_match.group(1).strip()

    # First 20 detailed patterns - extract from the summary line
    # Pattern: [🎯 First 20](link): 26% | 11 🐟 • 19%
    first20_detailed = re.search(r"\*\*:\*\* (\d+%) \| (\d+) ([🛠🐟🍤🐳🌱]) • (\d+%)", message_text)
    if first20_detailed:
        data["First 20 Percentage"] = first20_detailed.group(1)
        count = first20_detailed.group(2)
        emoji = first20_detailed.group(3)
        percentage = first20_detailed.group(4)
        data["First 20"] = f"{count} {emoji} • {percentage}"

    # Extract whale/fish patterns and analyze them
    whale_fish_patterns = extract_whale_fish_patterns(message_text)
    if whale_fish_patterns:
        data.update(whale_fish_patterns)

    return data

def extract_whale_fish_patterns(text):
    """Extract whale/fish emoji patterns"""
    patterns = {}
    
    # Extract the detailed holder pattern from the lines with solscan links
    # Look for pattern like: [🛠](https://solscan.io/account/...)[🐟](https://solscan.io/account/...)
    holder_pattern_match = re.findall(r'\[([🛠🐟🍤🐳🌱])\]\(https://solscan\.io/account/[^)]+\)', text)
    
    if holder_pattern_match:
        # Combine all holder emojis to create the full pattern
        all_patterns = ''.join(holder_pattern_match)
        patterns['Whale Fish Pattern'] = all_patterns
    
    return patterns

def parse_market_cap_value(mc_text):
    """Parse market cap text and return numeric value in thousands"""
    if not mc_text or mc_text == "N/A":
        return 0

    # Remove $ and convert to uppercase
    mc_clean = mc_text.replace("$", "").replace(",", "").upper()

    try:
        if "K" in mc_clean:
            # Convert K to thousands
            value = float(mc_clean.replace("K", ""))
            return value
        elif "M" in mc_clean:
            # Convert M to thousands (1M = 1000K)
            value = float(mc_clean.replace("M", ""))
            return value * 1000
        elif "B" in mc_clean:
            # Convert B to thousands (1B = 1,000,000K)
            value = float(mc_clean.replace("B", ""))
            return value * 1000000
        else:
            # Assume it's already in dollars, convert to thousands
            value = float(mc_clean)
            return value / 1000
    except (ValueError, AttributeError):
        return 0

def get_tmc_color(tmc_value_k):
    """Get color fill for T-MC based on value in thousands"""
    if 30 <= tmc_value_k <= 60:
        # Yellow
        return PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")
    elif 61 <= tmc_value_k <= 100:
        # Light Green
        return PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
    elif tmc_value_k >= 101:
        # Light Blue
        return PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")
    else:
        # No color for values below $30K
        return None

async def export_to_excel(data):
    """Export data to Excel with thread-safety."""
    async with processing_lock:
        try:
            wb = load_workbook(EXCEL_FILE)
            ws = wb.active

            # Check if token already exists
            token_exists = False
            for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[1] == data.get("Token Address"):  # Token Address is in column 2
                    token_exists = True
                    logger.info(f"⏩ Token {data['Token Address']} already exists, skipping")
                    return

            if not token_exists:
                # Use Morocco time for timestamp
                morocco_time = datetime.now(MOROCCO_TZ).strftime("%Y-%m-%d %H:%M:%S")
                row = [
                    # Your specified order first
                    morocco_time,
                    data["Token Address"],
                    data["Token Name"],
                    data["Profit"],
                    data["Warnings"],
                    data["Age"],
                    data["MC"],
                    data["T-MC"],
                    data["Liq"],
                    data["Liq SOL"],
                    data["First 20 Percentage"],
                    data["First 20"],
                    data["Whale Fish Pattern"],
                    data["Made"],
                    data["Volume"],
                    data["Price"],
                    data["Scans"],
                    data["Hodls"],
                    data["Top Holders"],
                    data["Snipers"],
                    data["Snipers Percentage"],
                    # Remaining columns in any order
                    data["Dex Paid"],
                    data["High"],
                    data["Dev"],
                    data["LP Burnt"],
                    data["Bundled"],
                    data["Airdrop"],
                    data["Burnt"],
                    data["Sold"],
                    data["Bond"]
                ]
                ws.append(row)

                # Apply profit column coloring
                current_row = ws.max_row
                profit_column_index = 4  # Profit is the 4th column
                profit_cell = ws.cell(row=current_row, column=profit_column_index)

                # Apply color based on profit value
                profit_value = data["Profit"]
                if profit_value and profit_value != "N/A" and profit_value != "Lost":
                    try:
                        if '%' in profit_value:
                            profit_num = float(profit_value.replace('%', '').strip())
                            if 50 <= profit_num < 100:
                                profit_cell.fill = GREY_FILL
                        elif 'X' in profit_value:
                            profit_num = float(profit_value.replace('X', '').strip())
                            if 2 <= profit_num < 3:
                                profit_cell.fill = YELLOW_FILL
                            elif 3 <= profit_num < 4:
                                profit_cell.fill = PURPLE_FILL
                            elif 5 <= profit_num < 6:
                                profit_cell.fill = RED_FILL
                            elif 7 <= profit_num < 8:
                                profit_cell.fill = ORANGE_FILL
                            elif 10 <= profit_num < 20:
                                profit_cell.fill = GREEN_FILL
                            elif profit_num >= 20:
                                profit_cell.fill = BLUE_FILL
                    except ValueError:
                        logger.warning(f"Could not parse profit value: {profit_value}")
            else:
                # Token exists, check if this is a profit update
                if len(data.keys()) <= 3 and "Profit" in data and data.get("Profit") != "N/A":
                    # This is just a profit update, update the profit column
                    for row_idx, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                        if row[1] == data.get("Token Address"):  # Token Address is in column 2
                            profit_col_idx = 4  # Profit is in column 4
                            ws.cell(row=row_idx, column=profit_col_idx, value=data["Profit"])
                            cell = ws.cell(row=row_idx, column=profit_col_idx)

                            # Apply color based on profit value
                            profit_value = data["Profit"]
                            if profit_value and profit_value != "N/A" and profit_value != "Lost":
                                try:
                                    if '%' in profit_value:
                                        profit_num = float(profit_value.replace('%', '').strip())
                                        if 50 <= profit_num < 100:
                                            cell.fill = GREY_FILL
                                    elif 'X' in profit_value:
                                        profit_num = float(profit_value.replace('X', '').strip())
                                        if 2 <= profit_num < 3:
                                            cell.fill = YELLOW_FILL
                                        elif 3 <= profit_num < 4:
                                            cell.fill = PURPLE_FILL
                                        elif 5 <= profit_num < 6:
                                            cell.fill = RED_FILL
                                        elif 7 <= profit_num < 8:
                                            cell.fill = ORANGE_FILL
                                        elif 10 <= profit_num < 20:
                                            cell.fill = GREEN_FILL
                                        elif profit_num >= 20:
                                            cell.fill = BLUE_FILL
                                except ValueError:
                                    logger.warning(f"Could not parse profit value: {profit_value}")

                            logger.info(f"✅ Updated profit data for existing token {data['Token Address']}")
                            break

                # Apply T-MC column coloring
                current_row = ws.max_row
                tmc_column_index = 7  # T-MC is the 7th column (index 6, but Excel is 1-based)
                tmc_cell = ws.cell(row=current_row, column=tmc_column_index)

                # Parse T-MC value and apply color
                tmc_value_k = parse_market_cap_value(data["T-MC"])
                tmc_color = get_tmc_color(tmc_value_k)

                if tmc_color:
                    tmc_cell.fill = tmc_color
                    logger.info(f"🎨 Applied color to T-MC: {data['T-MC']} (${tmc_value_k}K)")

                logger.info(f"✅ Added new row for token {data['Token Address']}")

            wb.save(EXCEL_FILE)
            logger.info(f"✅ Data saved to {EXCEL_FILE}")
        except Exception as e:
            logger.error(f"❌ Failed to export data to Excel: {e}")

async def forward_token(token_address):
    """Forward the token address to the destination."""
    try:
        await client.send_message(DESTINATION_BOT, token_address)
        logger.info(f"✅ Forwarded token to destination: {token_address}")
    except Exception as e:
        logger.error(f"❌ Failed to forward token: {e}")

async def process_one_message():
    """Fetch and process one message at a time."""
    global last_processed_message_id

    async with processing_lock:
        # Fetch message from source
        messages = await client.get_messages(SOURCE_USERNAME, limit=1)
        if not messages:
            logger.info("⚠️ No new messages found.")
            return

        message = messages[0]

        # Skip if already processed
        if last_processed_message_id and message.id <= last_processed_message_id:
            logger.info("⚠️ No new messages to process.")
            return

        last_processed_message_id = message.id
        message_text = message.message

        # Skip non-text messages
        if message_text is None:
            logger.info("⚠️ Message contains no text. Skipping...")
            return

        logger.info(f"📩 New message from {SOURCE_USERNAME}: {message_text}")

        # Check message type first
        message_text_lower = message_text.lower()

        # Check if this is a Type 1 message (New Trending)
        has_type1_pattern = any(pattern.lower() in message_text_lower for pattern in TYPE1_PATTERN)

        # Check if this is a Type 2 message (is up)
        has_type2_pattern = any(pattern.lower() in message_text_lower for pattern in TYPE2_PATTERN)

        if has_type1_pattern:
            logger.info("🔍 Type 1 message detected (New Trending)")

            # Extract token using Type 1 pattern
            token_match = re.search(TOKEN_PATTERN_TYPE1, message_text)
            if not token_match:
                logger.info("⚠️ No Type 1 token found in the message.")
                return

            token_address = token_match.group(1)

            # Check if already processed
            if token_address in processed_tokens:
                logger.info(f"⚠️ Token already processed: {token_address}")
                return

            # Blacklist check
            if BLACKLIST and any(word.lower() in message_text_lower for word in BLACKLIST):
                logger.info("⚠️ Message contains blacklist keywords.")
                return

            # Process Type 1 message (new token)
            processed_tokens.add(token_address)
            logger.info(f"🎯 Found new token: {token_address}")

            await forward_token(token_address)
            logger.info(f"⏳ Waiting for {WAIT_TIME} seconds...")
            await asyncio.sleep(WAIT_TIME)

            # Get response from destination bot
            dest_messages = await client.get_messages(DESTINATION_BOT, limit=1)
            if not dest_messages:
                logger.info("⚠️ No response from destination bot.")
                return

            last_dest_message = dest_messages[0]
            logger.info(f"📩 Destination bot response: {last_dest_message.text}")

            # Extract data from scanner response
            combined_data = extract_data_from_scanner_response(last_dest_message.text, token_address)

            # Export to Excel
            await export_to_excel(combined_data)
            logger.info("🛑 Finished processing new token.")

        elif has_type2_pattern:
            logger.info("🔍 Type 2 message detected (profit update)")

            # Extract token using Type 2 pattern
            token_match = re.search(TOKEN_PATTERN_TYPE2, message_text)
            if not token_match:
                logger.info("⚠️ No Type 2 token found in the message.")
                return

            token_address = token_match.group(1)

            # Extract profit value
            profit_value = extract_profit_from_message(message_text)
            if profit_value:
                logger.info(f"📊 Extracted profit: {profit_value} for token: {token_address}")

                # Create profit update data
                profit_data = {
                    "Token Address": token_address,
                    "Profit": profit_value
                }

                # Update Excel with profit data
                await export_to_excel(profit_data)
                logger.info("🛑 Finished processing profit update.")
            else:
                logger.info("⚠️ Could not extract profit value from message.")

        else:
            logger.info("⚠️ Message does not match Type 1 or Type 2 patterns, skipping.")

async def main():
    """Main function"""
    global client

    logger.info("🚀 Starting Early Scanner")
    logger.info(f"📍 Source: {SOURCE_USERNAME}")
    logger.info(f"📍 Destination: {DESTINATION_BOT}")
    logger.info(f"📊 Excel File: {EXCEL_FILE}")
    logger.info("=" * 50)

    # Initialize Excel file
    initialize_excel()

    # Initialize Telegram client
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)

    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")

        # Process messages in a loop
        while True:
            await process_one_message()
            await asyncio.sleep(RATE_LIMIT_DELAY)

    except Exception as e:
        logger.error(f"❌ Error in main: {e}")
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

if __name__ == "__main__":
    asyncio.run(main())
