#!/usr/bin/env python3
"""
Test if we can successfully forward tokens to @soul_scanner_bot and get responses
"""

from telethon import TelegramClient
import asyncio
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Early Scanner specific credentials
API_ID = os.getenv("EARLY_API_ID")
API_HASH = os.getenv("EARLY_API_HASH")
PHONE = os.getenv("EARLY_PHONE")

# Configuration
DESTINATION_BOT = "@soul_scanner_bot"
SESSION_FILE = "Early/Early_Scanner.session"

# Test token from your example
TEST_TOKEN = "6a2uYW1Tum5KxSmnjDoR4ZQfZNnckypWuhobFQRfpump"

# Logging configuration
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_soul_scanner_bot():
    """Test forwarding token to @soul_scanner_bot"""
    logger.info("🚀 Testing @soul_scanner_bot communication")
    logger.info(f"🎯 Test token: {TEST_TOKEN}")
    
    # Initialize Telegram client
    client = TelegramClient(SESSION_FILE.replace('.session', ''), API_ID, API_HASH)
    
    try:
        # Start the client
        await client.start(phone=PHONE)
        logger.info("✅ Client started successfully")
        
        # Test 1: Check if we can resolve the bot
        try:
            bot_entity = await client.get_entity(DESTINATION_BOT)
            logger.info(f"✅ Bot resolved: {bot_entity.username} (ID: {bot_entity.id})")
        except Exception as e:
            logger.error(f"❌ Failed to resolve bot {DESTINATION_BOT}: {e}")
            return
        
        # Test 2: Send the token to the bot
        logger.info(f"📤 Sending token to {DESTINATION_BOT}: {TEST_TOKEN}")
        try:
            await client.send_message(DESTINATION_BOT, TEST_TOKEN)
            logger.info("✅ Token sent successfully!")
        except Exception as e:
            logger.error(f"❌ Failed to send token: {e}")
            return
        
        # Test 3: Wait for response
        logger.info("⏳ Waiting 10 seconds for response...")
        await asyncio.sleep(10)
        
        # Test 4: Get the latest message from the bot
        try:
            messages = await client.get_messages(DESTINATION_BOT, limit=3)
            if messages:
                logger.info(f"📥 Found {len(messages)} recent messages from bot:")
                for i, msg in enumerate(messages):
                    logger.info(f"  Message {i+1} ({msg.date}): {msg.text[:100]}...")
                    
                    # Check if this message contains our token
                    if TEST_TOKEN in msg.text:
                        logger.info(f"🎯 Found response for our token!")
                        logger.info(f"📊 Full response: {msg.text}")
                        return msg.text
                    
                logger.info("⚠️ No response found for our specific token")
            else:
                logger.info("❌ No messages found from bot")
        except Exception as e:
            logger.error(f"❌ Failed to get messages from bot: {e}")
        
        # Test 5: Try sending /start command first
        logger.info("🔄 Trying to send /start command first...")
        try:
            await client.send_message(DESTINATION_BOT, "/start")
            await asyncio.sleep(3)
            
            # Try sending token again
            await client.send_message(DESTINATION_BOT, TEST_TOKEN)
            logger.info("✅ Token sent after /start command")
            
            # Wait and check again
            await asyncio.sleep(10)
            messages = await client.get_messages(DESTINATION_BOT, limit=3)
            if messages:
                for i, msg in enumerate(messages):
                    logger.info(f"  After /start - Message {i+1}: {msg.text[:100]}...")
                    if TEST_TOKEN in msg.text:
                        logger.info(f"🎯 Found response after /start!")
                        return msg.text
        except Exception as e:
            logger.error(f"❌ Failed with /start approach: {e}")
        
        return None
        
    except Exception as e:
        logger.error(f"❌ Error in test: {e}")
        return None
    finally:
        if client:
            await client.disconnect()
            logger.info("🔌 Client disconnected")

async def main():
    """Main test function"""
    logger.info("=" * 60)
    logger.info("🧪 TESTING @soul_scanner_bot COMMUNICATION")
    logger.info("=" * 60)
    
    response = await test_soul_scanner_bot()
    
    logger.info("=" * 60)
    if response:
        logger.info("✅ SUCCESS: Bot responded with data!")
        logger.info("💡 The Early_Scanner should be working if it's sending tokens correctly")
    else:
        logger.info("❌ ISSUE: Bot did not respond or respond with our token")
        logger.info("💡 This might be why the Early_Scanner sheet is empty")
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
