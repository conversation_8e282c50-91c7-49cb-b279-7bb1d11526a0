#!/usr/bin/env python3
"""
Test script to verify Early_Scanner token extraction functionality
"""

import re
from unittest.mock import Mock

# Token patterns from Early_Scanner.py
TOKEN_PATTERN_TYPE1 = r"https://t\.me/soul_sniper_bot\?start=15_([A-Za-z0-9]{43,44})"
TOKEN_PATTERN_TYPE2 = r"https://www\.geckoterminal\.com/solana/pools/([A-Za-z0-9]{43,44})"

def extract_token_address_from_message(message):
    """Extract token address from message text, entities, and buttons."""
    token_address = None
    
    # First, try to extract from message text (existing patterns)
    message_text = message.message or ""
    
    # Type 1 pattern in text
    token_match = re.search(TOKEN_PATTERN_TYPE1, message_text)
    if token_match:
        token_address = token_match.group(1)
        print(f"🎯 Found token in message text (Type 1): {token_address}")
        return token_address
    
    # Type 2 pattern in text
    token_match = re.search(TOKEN_PATTERN_TYPE2, message_text)
    if token_match:
        token_address = token_match.group(1)
        print(f"🎯 Found token in message text (Type 2): {token_address}")
        return token_address
    
    # Extract from message entities (URLs)
    if message.entities:
        for entity in message.entities:
            if hasattr(entity, 'url') and entity.url:
                url = entity.url
                
                # Check Type 1 pattern in entity URL
                token_match = re.search(TOKEN_PATTERN_TYPE1, url)
                if token_match:
                    token_address = token_match.group(1)
                    print(f"🎯 Found token in entity URL (Type 1): {token_address}")
                    return token_address
                
                # Check Type 2 pattern in entity URL
                token_match = re.search(TOKEN_PATTERN_TYPE2, url)
                if token_match:
                    token_address = token_match.group(1)
                    print(f"🎯 Found token in entity URL (Type 2): {token_address}")
                    return token_address
    
    # Extract from message buttons
    if message.buttons:
        for button_row in message.buttons:
            for button in button_row:
                if hasattr(button, 'url') and button.url:
                    url = button.url
                    
                    # Check Type 1 pattern in button URL
                    token_match = re.search(TOKEN_PATTERN_TYPE1, url)
                    if token_match:
                        token_address = token_match.group(1)
                        print(f"🎯 Found token in button URL (Type 1): {token_address}")
                        return token_address
                    
                    # Check Type 2 pattern in button URL
                    token_match = re.search(TOKEN_PATTERN_TYPE2, url)
                    if token_match:
                        token_address = token_match.group(1)
                        print(f"🎯 Found token in button URL (Type 2): {token_address}")
                        return token_address
    
    # Extract from reply markup (alternative way to access buttons)
    if hasattr(message, 'reply_markup') and message.reply_markup:
        if hasattr(message.reply_markup, 'rows'):
            for row in message.reply_markup.rows:
                if hasattr(row, 'buttons'):
                    for button in row.buttons:
                        if hasattr(button, 'url') and button.url:
                            url = button.url
                            
                            # Check Type 1 pattern in reply markup URL
                            token_match = re.search(TOKEN_PATTERN_TYPE1, url)
                            if token_match:
                                token_address = token_match.group(1)
                                print(f"🎯 Found token in reply markup URL (Type 1): {token_address}")
                                return token_address
                            
                            # Check Type 2 pattern in reply markup URL
                            token_match = re.search(TOKEN_PATTERN_TYPE2, url)
                            if token_match:
                                token_address = token_match.group(1)
                                print(f"🎯 Found token in reply markup URL (Type 2): {token_address}")
                                return token_address
    
    print("⚠️ No token address found in message, entities, or buttons")
    return None

def test_token_extraction():
    """Test token extraction with mock message data"""
    
    print("🧪 Testing token extraction functionality...")
    print("=" * 50)
    
    # Test 1: Token in entity URL (Type 2 - GeckoTerminal)
    print("\n📋 Test 1: Token in entity URL (GeckoTerminal)")
    mock_entity = Mock()
    mock_entity.url = "https://www.geckoterminal.com/solana/pools/6GvAiTFsc4V4RViFXdXpTqCusG5YACnwZjB81T6Hpump"
    
    mock_message = Mock()
    mock_message.message = "📈 DANTRUMP is up 50% 📈"
    mock_message.entities = [mock_entity]
    mock_message.buttons = None
    mock_message.reply_markup = None
    
    result = extract_token_address_from_message(mock_message)
    expected = "6GvAiTFsc4V4RViFXdXpTqCusG5YACnwZjB81T6Hpump"
    print(f"Expected: {expected}")
    print(f"Got: {result}")
    print(f"✅ PASS" if result == expected else f"❌ FAIL")
    
    # Test 2: Token in button URL (Type 1 - Soul Sniper Bot)
    print("\n📋 Test 2: Token in button URL (Soul Sniper Bot)")
    mock_button = Mock()
    mock_button.url = "https://t.me/soul_sniper_bot?start=15_6GvAiTFsc4V4RViFXdXpTqCusG5YACnwZjB81T6Hpump"
    
    mock_message2 = Mock()
    mock_message2.message = "📈 New Trending Token 📈"
    mock_message2.entities = None
    mock_message2.buttons = [[mock_button]]
    mock_message2.reply_markup = None
    
    result2 = extract_token_address_from_message(mock_message2)
    expected2 = "6GvAiTFsc4V4RViFXdXpTqCusG5YACnwZjB81T6Hpump"
    print(f"Expected: {expected2}")
    print(f"Got: {result2}")
    print(f"✅ PASS" if result2 == expected2 else f"❌ FAIL")
    
    # Test 3: No token found
    print("\n📋 Test 3: No token found")
    mock_message3 = Mock()
    mock_message3.message = "📈 Some random message 📈"
    mock_message3.entities = None
    mock_message3.buttons = None
    mock_message3.reply_markup = None
    
    result3 = extract_token_address_from_message(mock_message3)
    expected3 = None
    print(f"Expected: {expected3}")
    print(f"Got: {result3}")
    print(f"✅ PASS" if result3 == expected3 else f"❌ FAIL")
    
    print("\n" + "=" * 50)
    print("🎯 Token extraction tests completed!")

if __name__ == "__main__":
    test_token_extraction()
